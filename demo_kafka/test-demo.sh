#!/bin/bash

# Kafka + Spring Boot 3 Demo Test Script

echo "🚀 Testing Kafka + Spring Boot 3 Demo"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if service is running
check_service() {
    local url=$1
    local service_name=$2
    
    echo -e "${BLUE}Checking $service_name...${NC}"
    if curl -s "$url" > /dev/null; then
        echo -e "${GREEN}✅ $service_name is running${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name is not running${NC}"
        return 1
    fi
}

# Function to send test message
send_test_message() {
    local endpoint=$1
    local data=$2
    local description=$3
    
    echo -e "${BLUE}Testing: $description${NC}"
    response=$(curl -s -X POST "$endpoint" \
        -H "Content-Type: application/json" \
        -d "$data")
    
    if echo "$response" | grep -q "success"; then
        echo -e "${GREEN}✅ $description - SUCCESS${NC}"
    else
        echo -e "${RED}❌ $description - FAILED${NC}"
        echo "Response: $response"
    fi
}

echo -e "${YELLOW}Step 1: Checking if services are running...${NC}"
echo ""

# Check Producer
check_service "http://localhost:8081/api/messages/health" "Producer (8081)"
producer_running=$?

# Check Consumer  
check_service "http://localhost:8082/api/consumer/health" "Consumer (8082)"
consumer_running=$?

# Check Kafka UI
check_service "http://localhost:8080" "Kafka UI (8080)"
kafka_ui_running=$?

echo ""

if [ $producer_running -ne 0 ] || [ $consumer_running -ne 0 ]; then
    echo -e "${RED}❌ Some services are not running. Please start them first.${NC}"
    echo ""
    echo "To start services:"
    echo "1. cd demo_kafka && docker-compose up -d"
    echo "2. cd kafka-consumer && mvn spring-boot:run"
    echo "3. cd kafka-producer && mvn spring-boot:run"
    exit 1
fi

echo -e "${YELLOW}Step 2: Sending test messages...${NC}"
echo ""

# Test User Message
send_test_message "http://localhost:8081/api/messages/user" \
    '{"id": 1, "name": "John Doe", "email": "<EMAIL>", "action": "CREATE"}' \
    "User Message (CREATE)"

sleep 1

send_test_message "http://localhost:8081/api/messages/user" \
    '{"id": 2, "name": "Jane Smith", "email": "<EMAIL>", "action": "UPDATE"}' \
    "User Message (UPDATE)"

sleep 1

# Test Notification Message
send_test_message "http://localhost:8081/api/messages/notification" \
    '{"message": "Welcome to our platform!", "type": "INFO", "recipient": "<EMAIL>"}' \
    "Notification Message (INFO)"

sleep 1

send_test_message "http://localhost:8081/api/messages/notification" \
    '{"message": "Your account needs attention", "type": "WARNING", "recipient": "<EMAIL>"}' \
    "Notification Message (WARNING)"

sleep 1

# Test System Log
echo -e "${BLUE}Testing: System Log${NC}"
response=$(curl -s -X POST "http://localhost:8081/api/messages/log?level=INFO&message=Demo test completed successfully")
if echo "$response" | grep -q "success"; then
    echo -e "${GREEN}✅ System Log - SUCCESS${NC}"
else
    echo -e "${RED}❌ System Log - FAILED${NC}"
fi

echo ""
echo -e "${YELLOW}Step 3: Waiting for message processing...${NC}"
sleep 3

echo ""
echo -e "${YELLOW}Step 4: Checking processed messages...${NC}"
echo ""

# Check processed user messages
echo -e "${BLUE}Checking processed user messages...${NC}"
user_messages=$(curl -s "http://localhost:8082/api/consumer/users")
user_count=$(echo "$user_messages" | jq '. | length' 2>/dev/null || echo "0")
echo -e "${GREEN}📊 Processed user messages: $user_count${NC}"

# Check processed notification messages
echo -e "${BLUE}Checking processed notification messages...${NC}"
notification_messages=$(curl -s "http://localhost:8082/api/consumer/notifications")
notification_count=$(echo "$notification_messages" | jq '. | length' 2>/dev/null || echo "0")
echo -e "${GREEN}📊 Processed notification messages: $notification_count${NC}"

# Get consumer statistics
echo -e "${BLUE}Getting consumer statistics...${NC}"
stats=$(curl -s "http://localhost:8082/api/consumer/stats")
echo -e "${GREEN}📈 Consumer Statistics:${NC}"
echo "$stats" | jq '.' 2>/dev/null || echo "$stats"

echo ""
echo -e "${YELLOW}Step 5: Demo URLs for manual testing:${NC}"
echo ""
echo -e "${BLUE}🌐 Kafka UI:${NC} http://localhost:8080"
echo -e "${BLUE}📤 Producer Health:${NC} http://localhost:8081/api/messages/health"
echo -e "${BLUE}📥 Consumer Health:${NC} http://localhost:8082/api/consumer/health"
echo -e "${BLUE}📊 Consumer Stats:${NC} http://localhost:8082/api/consumer/stats"
echo -e "${BLUE}🗄️ H2 Console:${NC} http://localhost:8082/h2-console"
echo ""
echo -e "${GREEN}🎉 Demo test completed!${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Visit Kafka UI to see topics and messages"
echo "2. Check H2 Console to see stored data"
echo "3. Send more messages using the API endpoints"
echo "4. Monitor logs in both Producer and Consumer applications"
