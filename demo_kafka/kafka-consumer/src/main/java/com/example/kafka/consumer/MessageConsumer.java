package com.example.kafka.consumer;

import com.example.kafka.model.NotificationMessage;
import com.example.kafka.model.UserMessage;
import com.example.kafka.service.MessageProcessingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

/**
 * Kafka Message Consumer Service
 */
@Service
public class MessageConsumer {

    private static final Logger logger = LoggerFactory.getLogger(MessageConsumer.class);

    private final MessageProcessingService messageProcessingService;

    public MessageConsumer(MessageProcessingService messageProcessingService) {
        this.messageProcessingService = messageProcessingService;
    }

    /**
     * Consume user messages from user-events topic
     */
    @KafkaListener(
        topics = "${kafka.topic.user-events}",
        groupId = "${spring.kafka.consumer.group-id}",
        containerFactory = "userMessageKafkaListenerContainerFactory"
    )
    public void consumeUserMessage(
            @Payload UserMessage userMessage,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            Acknowledgment acknowledgment) {
        
        logger.info("📥 Received user message from topic '{}' [partition: {}, offset: {}]: {}", 
                   topic, partition, offset, userMessage);
        
        try {
            // Process the message
            messageProcessingService.processUserMessage(userMessage);
            
            // Acknowledge the message
            if (acknowledgment != null) {
                acknowledgment.acknowledge();
            }
            
            logger.info("✅ User message processed successfully: {}", userMessage.getId());
        } catch (Exception e) {
            logger.error("❌ Error processing user message: {}", userMessage.getId(), e);
            // In a real application, you might want to send to a dead letter queue
        }
    }

    /**
     * Consume notification messages from notifications topic
     */
    @KafkaListener(
        topics = "${kafka.topic.notifications}",
        groupId = "${spring.kafka.consumer.group-id}",
        containerFactory = "notificationMessageKafkaListenerContainerFactory"
    )
    public void consumeNotificationMessage(
            @Payload NotificationMessage notificationMessage,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            Acknowledgment acknowledgment) {
        
        logger.info("📥 Received notification message from topic '{}' [partition: {}, offset: {}]: {}", 
                   topic, partition, offset, notificationMessage);
        
        try {
            // Process the message
            messageProcessingService.processNotificationMessage(notificationMessage);
            
            // Acknowledge the message
            if (acknowledgment != null) {
                acknowledgment.acknowledge();
            }
            
            logger.info("✅ Notification message processed successfully: {}", notificationMessage.getId());
        } catch (Exception e) {
            logger.error("❌ Error processing notification message: {}", notificationMessage.getId(), e);
        }
    }

    /**
     * Consume system logs from system-logs topic
     */
    @KafkaListener(
        topics = "${kafka.topic.system-logs}",
        groupId = "${spring.kafka.consumer.group-id}",
        containerFactory = "stringKafkaListenerContainerFactory"
    )
    public void consumeSystemLog(
            @Payload String logMessage,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            @Header(KafkaHeaders.RECEIVED_KEY) String key,
            Acknowledgment acknowledgment) {
        
        logger.info("📥 Received system log from topic '{}' [partition: {}, offset: {}, key: {}]: {}", 
                   topic, partition, offset, key, logMessage);
        
        try {
            // Process the log message
            messageProcessingService.processSystemLog(key, logMessage);
            
            // Acknowledge the message
            if (acknowledgment != null) {
                acknowledgment.acknowledge();
            }
            
            logger.info("✅ System log processed successfully");
        } catch (Exception e) {
            logger.error("❌ Error processing system log", e);
        }
    }
}
