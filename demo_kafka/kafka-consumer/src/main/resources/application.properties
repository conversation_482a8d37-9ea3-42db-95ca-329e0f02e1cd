# Kafka Consumer Configuration
spring.application.name=kafka-consumer
server.port=8082

# Kafka Bootstrap Servers
spring.kafka.bootstrap-servers=localhost:9092

# Consumer Configuration
spring.kafka.consumer.bootstrap-servers=localhost:9092
spring.kafka.consumer.group-id=demo-consumer-group
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.springframework.kafka.support.serializer.JsonDeserializer

# JSON Deserializer Configuration
spring.kafka.consumer.properties.spring.json.trusted.packages=com.example.kafka.model
spring.kafka.consumer.properties.spring.json.use.type.headers=false
spring.kafka.consumer.properties.spring.json.value.default.type=com.example.kafka.model.UserMessage

# Consumer Performance Settings
spring.kafka.consumer.fetch-min-size=1
spring.kafka.consumer.fetch-max-wait=500ms
spring.kafka.consumer.max-poll-records=500
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.auto-commit-interval=1000ms

# Topic Configuration
kafka.topic.user-events=user-events
kafka.topic.notifications=notifications
kafka.topic.system-logs=system-logs

# Database Configuration (H2)
spring.datasource.url=jdbc:h2:mem:consumerdb
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# H2 Console
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Logging Configuration
logging.level.com.example.kafka=INFO
logging.level.org.springframework.kafka=INFO
logging.level.org.apache.kafka=WARN
logging.level.org.hibernate.SQL=DEBUG

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,kafka
management.endpoint.health.show-details=always

# Application Info
info.app.name=Kafka Consumer Demo
info.app.description=Spring Boot 3 Kafka Consumer Demo Application
info.app.version=1.0.0
