-------------------------------------------------------------------------------
Test set: com.circlelife.repository.UserProfileRepositoryTest
-------------------------------------------------------------------------------
Tests run: 1, Failures: 1, Errors: 0, Skipped: 0, Time elapsed: 8.919 s <<< FAILURE! -- in com.circlelife.repository.UserProfileRepositoryTest
com.circlelife.repository.UserProfileRepositoryTest.findByProfileType_ShouldReturnProfiles_WhenTypeMatches -- Time elapsed: 0.950 s <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <1> but was: <6>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:150)
	at org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:145)
	at org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:531)
	at com.circlelife.repository.UserProfileRepositoryTest.findByProfileType_ShouldReturnProfiles_WhenTypeMatches(UserProfileRepositoryTest.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

