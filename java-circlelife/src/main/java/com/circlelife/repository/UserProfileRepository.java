package com.circlelife.repository;

import com.circlelife.model.UserProfile;
import com.circlelife.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * UserProfile Repository demonstrating advanced JPA features
 * 
 * @Repository - Marks this interface as a Spring Data repository
 * Extends JpaRepository for CRUD operations and custom query methods
 */
@Repository
public interface UserProfileRepository extends JpaRepository<UserProfile, Long> {

    /**
     * Find profiles by user
     * Method name convention: findBy + PropertyName
     */
    List<UserProfile> findByUser(User user);

    /**
     * Find profiles by user ID
     * Method name convention: findBy + PropertyPath
     */
    List<UserProfile> findByUserId(Long userId);

    /**
     * Find profiles by profile type
     * Method name convention: findBy + PropertyName
     */
    List<UserProfile> findByProfileType(UserProfile.ProfileType profileType);

    /**
     * Find profiles by user and profile type
     * Method name convention: findBy + PropertyName + And + PropertyName
     */
    List<UserProfile> findByUserAndProfileType(User user, UserProfile.ProfileType profileType);

    /**
     * Find profiles by user ID and profile type
     */
    List<UserProfile> findByUserIdAndProfileType(Long userId, UserProfile.ProfileType profileType);

    /**
     * Find active profiles by user
     * Method name convention: findBy + PropertyName + And + PropertyName
     */
    List<UserProfile> findByUserAndIsActiveTrue(User user);

    /**
     * Find profiles by profile key containing (case-insensitive)
     * Method name convention: findBy + PropertyName + IgnoreCase + Containing
     */
    List<UserProfile> findByProfileKeyIgnoreCaseContaining(String profileKey);

    /**
     * Find profiles created after a specific date
     * Method name convention: findBy + PropertyName + After
     */
    List<UserProfile> findByCreatedAtAfter(LocalDateTime date);

    /**
     * Find profiles by user with pagination
     */
    Page<UserProfile> findByUser(User user, Pageable pageable);

    /**
     * Find profiles by profile type with pagination
     */
    Page<UserProfile> findByProfileType(UserProfile.ProfileType profileType, Pageable pageable);

    /**
     * Check if profile exists by user and profile key
     * Method name convention: existsBy + PropertyName + And + PropertyName
     */
    boolean existsByUserAndProfileKey(User user, String profileKey);

    /**
     * Count profiles by user
     * Method name convention: countBy + PropertyName
     */
    long countByUser(User user);

    /**
     * Count profiles by profile type
     */
    long countByProfileType(UserProfile.ProfileType profileType);

    /**
     * Count active profiles by user
     */
    long countByUserAndIsActiveTrue(User user);

    /**
     * Delete profiles by user and profile type
     * Method name convention: deleteBy + PropertyName + And + PropertyName
     */
    void deleteByUserAndProfileType(User user, UserProfile.ProfileType profileType);

    /**
     * Custom JPQL Query - Find profiles with specific criteria
     * @Query - Defines a custom JPQL query
     * @Param - Binds method parameter to query parameter
     */
    @Query("SELECT up FROM UserProfile up WHERE up.user.id = :userId AND up.isActive = true ORDER BY up.createdAt DESC")
    List<UserProfile> findActiveProfilesByUserId(@Param("userId") Long userId);

    /**
     * Custom JPQL Query - Find profiles by multiple types
     */
    @Query("SELECT up FROM UserProfile up WHERE up.profileType IN :types AND up.isActive = true")
    List<UserProfile> findActiveProfilesByTypes(@Param("types") List<UserProfile.ProfileType> types);

    /**
     * Custom JPQL Query - Find profiles with value containing text
     */
    @Query("SELECT up FROM UserProfile up WHERE LOWER(up.profileValue) LIKE LOWER(CONCAT('%', :searchText, '%'))")
    List<UserProfile> findProfilesByValueContaining(@Param("searchText") String searchText);

    /**
     * Custom JPQL Query - Get profile statistics by type
     */
    @Query("SELECT up.profileType, COUNT(up) FROM UserProfile up WHERE up.isActive = true GROUP BY up.profileType")
    List<Object[]> getProfileStatisticsByType();

    /**
     * Custom JPQL Query - Find user profiles with user information
     */
    @Query("SELECT up FROM UserProfile up JOIN FETCH up.user u WHERE u.status = 'ACTIVE' AND up.isActive = true")
    List<UserProfile> findActiveProfilesWithActiveUsers();

    /**
     * Native SQL Query - Find profiles created in last N days
     * @Query with nativeQuery = true allows raw SQL
     */
    @Query(value = "SELECT * FROM user_profiles WHERE created_at >= DATE_SUB(NOW(), INTERVAL :days DAY) AND is_active = true", 
           nativeQuery = true)
    List<UserProfile> findRecentActiveProfiles(@Param("days") int days);

    /**
     * Custom update query - Deactivate profiles by user
     * @Modifying - Indicates this query modifies data
     * @Query - Custom update query
     */
    @Modifying
    @Query("UPDATE UserProfile up SET up.isActive = false WHERE up.user = :user")
    int deactivateProfilesByUser(@Param("user") User user);

    /**
     * Custom update query - Update profile value
     */
    @Modifying
    @Query("UPDATE UserProfile up SET up.profileValue = :value, up.updatedAt = CURRENT_TIMESTAMP WHERE up.id = :id")
    int updateProfileValue(@Param("id") Long id, @Param("value") String value);

    /**
     * Custom delete query - Delete inactive profiles older than specified days
     */
    @Modifying
    @Query("DELETE FROM UserProfile up WHERE up.isActive = false AND up.updatedAt < :cutoffDate")
    int deleteInactiveProfilesOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Find profile by user and profile key (unique combination)
     */
    Optional<UserProfile> findByUserAndProfileKey(User user, String profileKey);

    /**
     * Find the latest profile by user and type
     */
    @Query("SELECT up FROM UserProfile up WHERE up.user = :user AND up.profileType = :type ORDER BY up.createdAt DESC")
    Optional<UserProfile> findLatestProfileByUserAndType(@Param("user") User user, 
                                                         @Param("type") UserProfile.ProfileType type);

    /**
     * Custom query for profile search with multiple criteria
     */
    @Query("SELECT up FROM UserProfile up WHERE " +
           "(:userId IS NULL OR up.user.id = :userId) AND " +
           "(:profileType IS NULL OR up.profileType = :profileType) AND " +
           "(:isActive IS NULL OR up.isActive = :isActive) AND " +
           "(:searchText IS NULL OR up.profileKey LIKE CONCAT('%', :searchText, '%') OR " +
           " up.profileValue LIKE CONCAT('%', :searchText, '%'))")
    Page<UserProfile> searchProfiles(@Param("userId") Long userId,
                                   @Param("profileType") UserProfile.ProfileType profileType,
                                   @Param("isActive") Boolean isActive,
                                   @Param("searchText") String searchText,
                                   Pageable pageable);
}
